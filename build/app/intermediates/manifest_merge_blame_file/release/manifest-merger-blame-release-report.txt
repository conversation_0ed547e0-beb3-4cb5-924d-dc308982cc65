1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.hello_world_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
8-->/app/android/app/src/main/AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->/app/android/app/src/main/AndroidManifest.xml
10
11    <!-- Camera permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->/app/android/app/src/main/AndroidManifest.xml:5:5-65
12-->/app/android/app/src/main/AndroidManifest.xml:5:22-62
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->/app/android/app/src/main/AndroidManifest.xml:6:5-81
13-->/app/android/app/src/main/AndroidManifest.xml:6:22-78
14    <uses-permission android:name="android.permission.RECORD_AUDIO" />
14-->[:camera_android] /app/build/camera_android/intermediates/merged_manifest/release/AndroidManifest.xml:10:5-71
14-->[:camera_android] /app/build/camera_android/intermediates/merged_manifest/release/AndroidManifest.xml:10:22-68
15
16    <application
17        android:name="android.app.Application"
17-->/app/android/app/src/main/AndroidManifest.xml:10:9-42
18        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
18-->[androidx.core:core:1.6.0] /root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/AndroidManifest.xml:24:18-86
19        android:extractNativeLibs="false"
20        android:icon="@mipmap/ic_launcher"
20-->/app/android/app/src/main/AndroidManifest.xml:11:9-43
21        android:label="hello_world_app" >
21-->/app/android/app/src/main/AndroidManifest.xml:9:9-40
22        <activity
22-->/app/android/app/src/main/AndroidManifest.xml:12:9-32:20
23            android:name="com.example.hello_world_app.MainActivity"
23-->/app/android/app/src/main/AndroidManifest.xml:13:13-41
24            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
24-->/app/android/app/src/main/AndroidManifest.xml:17:13-163
25            android:exported="true"
25-->/app/android/app/src/main/AndroidManifest.xml:14:13-36
26            android:hardwareAccelerated="true"
26-->/app/android/app/src/main/AndroidManifest.xml:18:13-47
27            android:launchMode="singleTop"
27-->/app/android/app/src/main/AndroidManifest.xml:15:13-43
28            android:theme="@style/LaunchTheme"
28-->/app/android/app/src/main/AndroidManifest.xml:16:13-47
29            android:windowSoftInputMode="adjustResize" >
29-->/app/android/app/src/main/AndroidManifest.xml:19:13-55
30
31            <!--
32                 Specifies an Android theme to apply to this Activity as soon as
33                 the Android process has started. This theme is visible to the user
34                 while the Flutter UI initializes. After that, this theme continues
35                 to determine the Window background behind the Flutter UI.
36            -->
37            <meta-data
37-->/app/android/app/src/main/AndroidManifest.xml:24:13-27:17
38                android:name="io.flutter.embedding.android.NormalTheme"
38-->/app/android/app/src/main/AndroidManifest.xml:25:15-70
39                android:resource="@style/NormalTheme" />
39-->/app/android/app/src/main/AndroidManifest.xml:26:15-52
40
41            <intent-filter>
41-->/app/android/app/src/main/AndroidManifest.xml:28:13-31:29
42                <action android:name="android.intent.action.MAIN" />
42-->/app/android/app/src/main/AndroidManifest.xml:29:17-68
42-->/app/android/app/src/main/AndroidManifest.xml:29:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->/app/android/app/src/main/AndroidManifest.xml:30:17-76
44-->/app/android/app/src/main/AndroidManifest.xml:30:27-74
45            </intent-filter>
46        </activity>
47        <!--
48             Don't delete the meta-data below.
49             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
50        -->
51        <meta-data
51-->/app/android/app/src/main/AndroidManifest.xml:35:9-37:33
52            android:name="flutterEmbedding"
52-->/app/android/app/src/main/AndroidManifest.xml:36:13-44
53            android:value="2" />
53-->/app/android/app/src/main/AndroidManifest.xml:37:13-30
54
55        <uses-library
55-->[androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:25:9-27:40
56            android:name="androidx.window.extensions"
56-->[androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:26:13-54
57            android:required="false" />
57-->[androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:27:13-37
58        <uses-library
58-->[androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:28:9-30:40
59            android:name="androidx.window.sidecar"
59-->[androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:29:13-51
60            android:required="false" />
60-->[androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:30:13-37
61    </application>
62
63</manifest>
