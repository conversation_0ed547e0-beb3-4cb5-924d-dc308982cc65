<variant
    name="release"
    package="com.example.hello_world_app"
    minSdkVersion="26"
    targetSdkVersion="33"
    shrinking="true"
    mergedManifest="/app/build/app/intermediates/merged_manifest/release/AndroidManifest.xml"
    proguardFiles="/app/build/app/intermediates/default_proguard_files/global/proguard-android.txt-7.2.0:/sdks/flutter/packages/flutter_tools/gradle/flutter_proguard_rules.pro:proguard-rules.pro"
    partialResultsDir="/app/build/app/intermediates/lint_vital_partial_results/release/out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifest="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/main/kotlin"
        resDirectories="src/main/res"
        assetsDirectories="src/main/assets"/>
    <sourceProvider
        manifest="src/release/AndroidManifest.xml"
        javaDirectories="src/release/java:src/release/kotlin"
        resDirectories="src/release/res"
        assetsDirectories="src/release/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <mainArtifact
      classOutputs="/app/build/app/intermediates/javac/release/classes:/app/build/app/tmp/kotlin-classes/release:/app/build/app/kotlinToolingMetadata:/app/build/app/intermediates/compile_and_runtime_not_namespaced_r_class_jar/release/R.jar"
      applicationId="com.example.hello_world_app"
      generatedSourceFolders="/app/build/app/generated/source/buildConfig/release:/app/build/app/generated/aidl_source_output_dir/release/out:/app/build/app/generated/renderscript_source_output_dir/release/out:/app/build/app/generated/ap_generated_sources/release/out"
      generatedResourceFolders="/app/build/app/generated/res/rs/release:/app/build/app/generated/res/resValues/release">
  </mainArtifact>
</variant>
