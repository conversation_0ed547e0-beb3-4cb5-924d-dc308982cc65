<lint-module
    format="1"
    dir="/app/android/app"
    name=":app"
    type="APP"
    maven="android:app:"
    gradle="7.2.0"
    buildFolder="/app/build/app"
    bootClassPath="/opt/android-sdk-linux/platforms/android-33/android.jar:/opt/android-sdk-linux/build-tools/30.0.3/core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-33">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
