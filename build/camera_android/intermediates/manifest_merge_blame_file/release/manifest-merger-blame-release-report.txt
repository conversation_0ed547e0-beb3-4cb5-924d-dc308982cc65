1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.flutter.plugins.camera" >
4
5    <uses-sdk
6        android:minSdkVersion="21"
6-->/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/android/src/main/AndroidManifest.xml
7        android:targetSdkVersion="21" />
7-->/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/android/src/main/AndroidManifest.xml
8
9    <uses-permission android:name="android.permission.CAMERA" />
9-->/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/android/src/main/AndroidManifest.xml:3:5-64
9-->/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/android/src/main/AndroidManifest.xml:3:22-62
10    <uses-permission android:name="android.permission.RECORD_AUDIO" />
10-->/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/android/src/main/AndroidManifest.xml:4:5-70
10-->/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/android/src/main/AndroidManifest.xml:4:22-68
11
12</manifest>
