{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/", "native_build": true, "dependencies": []}], "android": [{"name": "camera_android", "path": "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "flutter_plugin_android_lifecycle", "path": "/root/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17/", "native_build": true, "dependencies": []}], "macos": [], "linux": [], "windows": [], "web": [{"name": "camera_web", "path": "/root/.pub-cache/hosted/pub.dev/camera_web-0.3.2+2/", "dependencies": []}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}], "date_created": "2025-06-02 18:44:03.531311", "version": "3.7.7"}