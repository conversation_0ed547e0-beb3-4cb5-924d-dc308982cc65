//
// Generated file. Do not edit.
// This file is generated from template in file `flutter_tools/lib/src/flutter_plugins.dart`.
//

// @dart = 2.19

import 'dart:io'; // flutter_ignore: dart_io_import.
import 'package:camera_android/camera_android.dart';
import 'package:camera_avfoundation/camera_avfoundation.dart';

@pragma('vm:entry-point')
class _PluginRegistrant {

  @pragma('vm:entry-point')
  static void register() {
    if (Platform.isAndroid) {
      try {
        AndroidCamera.registerWith();
      } catch (err) {
        print(
          '`camera_android` threw an error: $err. '
          'The app may not function as expected until you remove this plugin from pubspec.yaml'
        );
        rethrow;
      }

    } else if (Platform.isIOS) {
      try {
        AVFoundationCamera.registerWith();
      } catch (err) {
        print(
          '`camera_avfoundation` threw an error: $err. '
          'The app may not function as expected until you remove this plugin from pubspec.yaml'
        );
        rethrow;
      }

    } else if (Platform.isLinux) {
    } else if (Platform.isMacOS) {
    } else if (Platform.isWindows) {
    }
  }
}
