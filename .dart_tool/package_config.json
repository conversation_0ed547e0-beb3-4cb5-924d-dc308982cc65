{"configVersion": 2, "packages": [{"name": "async", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/async-2.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "camera", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/camera-0.10.5+5", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "camera_android", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "camera_avfoundation", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "camera_platform_interface", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "camera_web", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/camera_web-0.3.2+2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "characters", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/characters-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "clock", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/collection-1.17.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "cross_file", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cupertino_icons", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fake_async", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter", "rootUri": "file:///sdks/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_lints", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_test", "rootUri": "file:///sdks/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_web_plugins", "rootUri": "file:///sdks/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "js", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/js-0.6.5", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "lints", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/lints-2.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "matcher", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/matcher-0.12.13", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "material_color_utilities", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0", "packageUri": "lib/", "languageVersion": "2.13"}, {"name": "meta", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/meta-1.8.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/path-1.8.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "plugin_platform_interface", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "quiver", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/quiver-3.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "sky_engine", "rootUri": "file:///sdks/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "source_span", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/source_span-1.9.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "stack_trace", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/stack_trace-1.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stream_channel", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/stream_channel-2.1.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "stream_transform", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "string_scanner", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "term_glyph", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/test_api-0.4.16", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "vector_math", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "hello_world_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "2.19"}], "generated": "2025-06-02T21:03:34.415381Z", "generator": "pub", "generatorVersion": "2.19.4"}