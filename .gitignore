# Build outputs
build/
.dart_tool/
*.dill
*.so
*.dylib
*.dll

# Flutter/Dart specific
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
pubspec.lock

# IDE files
.vscode/
.idea/
*.iml
*.ipr
*.iws
.project
.classpath
.settings/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile
docker-compose.yml
docker-compose.*.yml

# Logs
*.log
logs/

# Android specific (generated files)
android/.gradle/
android/app/build/
android/build/
android/gradle/
android/gradlew
android/gradlew.bat
android/local.properties
android/key.properties
android/.idea/
android/captures/
android/app/src/main/java/io/flutter/plugins/

# iOS specific (if added later)
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/Flutter/Generated.xcconfig
ios/Flutter/app.flx
ios/Flutter/app.zip
ios/Flutter/flutter_assets/
ios/ServiceDefinitions.json
ios/Runner/GeneratedPluginRegistrant.*
ios/Pods/
ios/.symlinks/
ios/Flutter/ephemeral/

# Web specific (if added later)
web/flutter_service_worker.js

# Coverage
coverage/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Syncthing
.stfolder/
.stignore