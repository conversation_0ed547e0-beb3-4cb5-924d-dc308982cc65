# Build outputs
build/
.dart_tool/
*.dill
*.so
*.dylib
*.dll

# Flutter/Dart specific
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
pubspec.lock

# IDE files
.vscode/
.idea/
*.iml
*.ipr
*.iws
.project
.classpath
.settings/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile
docker-compose.yml
docker-compose.*.yml

# Logs
*.log
logs/

# Android specific (generated files)
android/.gradle/
android/app/build/
android/build/
android/gradle/
android/gradlew
android/gradlew.bat
android/local.properties
android/key.properties
android/.idea/
android/captures/
android/app/src/main/java/io/flutter/plugins/

# iOS specific (if added later)
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/Flutter/Generated.xcconfig
ios/Flutter/app.flx
ios/Flutter/app.zip
ios/Flutter/flutter_assets/
ios/ServiceDefinitions.json
ios/Runner/GeneratedPluginRegistrant.*
ios/Pods/
ios/.symlinks/
ios/Flutter/ephemeral/

# Web specific (if added later)
web/flutter_service_worker.js

# Coverage
coverage/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Syncthing
.stfolder/
.stignore
build/app/intermediates/assets/release/mergeReleaseAssets/flutter_assets/NOTICES.Z
build/app/intermediates/assets/release/mergeReleaseAssets/flutter_assets/fonts/MaterialIcons-Regular.otf
build/app/intermediates/bundle_manifest/release/AndroidManifest.xml
build/app/intermediates/compile_and_runtime_not_namespaced_r_class_jar/release/R.jar
build/app/intermediates/compressed_assets/release/out/assets/flutter_assets/NOTICES.Z.jar
build/app/intermediates/compressed_assets/release/out/assets/flutter_assets/fonts/MaterialIcons-Regular.otf.jar
build/app/intermediates/dex/release/minifyReleaseWithR8/classes.dex
build/app/intermediates/flutter/release/flutter_build.d
build/app/intermediates/flutter/release/libs.jar
build/app/intermediates/flutter/release/arm64-v8a/app.so
build/app/intermediates/flutter/release/armeabi-v7a/app.so
build/app/intermediates/flutter/release/flutter_assets/NOTICES.Z
build/app/intermediates/flutter/release/flutter_assets/fonts/MaterialIcons-Regular.otf
build/app/intermediates/flutter/release/x86_64/app.so
build/app/intermediates/incremental/lintVitalAnalyzeRelease/release-mainArtifact-dependencies.xml
build/app/intermediates/incremental/lintVitalAnalyzeRelease/release-mainArtifact-libraries.xml
build/app/intermediates/incremental/lintVitalAnalyzeRelease/release.xml
build/app/intermediates/incremental/lintVitalReportRelease/release-mainArtifact-dependencies.xml
build/app/intermediates/incremental/lintVitalReportRelease/release-mainArtifact-libraries.xml
build/app/intermediates/incremental/lintVitalReportRelease/release.xml
build/app/intermediates/incremental/mergeReleaseAssets/merger.xml
build/app/intermediates/incremental/packageRelease/tmp/release/dex-renamer-state.txt
build/app/intermediates/incremental/packageRelease/tmp/release/zip-cache/androidResources
build/app/intermediates/incremental/release/mergeReleaseResources/compile-file-map.properties
build/app/intermediates/incremental/release/mergeReleaseResources/merger.xml
build/app/intermediates/incremental/release-mergeJavaRes/merge-state
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/0z0Ufi1Lfa8843hIHHbNMjvyqQU=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/bbg2TqjK3SSvl0UtcN924A17UpI=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/foOeMc5k2gESPRS1HHmwyKMshJI=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/gOO501qVlxODhaU3n9qu1iCiIMw=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/gSx2HmOxQieynI6MM7TAhMRH3K4=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/I+8Z7Q5OitO5uMHiELd_NXA5AI4=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/Izb5Btu9tWoj88U8T0fy2WMYcso=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/LSvQVTWU2XSBtDcRjHAoF4SDtWQ=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/M3HYDUNH6IMU4uWLvOpU1R+RHeU=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/oFFsV4dVfOoG0_puHz+obn0mWKs=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/QIJD6Rzcypz7y_oAeNpBUQXyPgo=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/ReM9EJ7eLyXqoh1m4EFbXaH6Y9o=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/t4wl3PGDhHVH8b2+WX2RqyBFk7o=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/uQhEnPyTcUIJj7r7wJVLAJSLUPs=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/VCOvPvwuAyGOHFScEaZHJ0CKUCQ=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/wkB8Du9puXU96vYEgO2parbPFPI=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/wltKVelwyO4W1Mgk7KINFf5acwk=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/xm3bgRugRl9IBlTBQFIMLL+o2HU=
build/app/intermediates/incremental/release-mergeJavaRes/zip-cache/xqjD3AmbZ5_uPwEjMbjfkWvEGo4=
build/app/intermediates/javac/release/classes/io/flutter/plugins/GeneratedPluginRegistrant.class
build/app/intermediates/linked_res_for_bundle/release/bundled-res.ap_
build/app/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
build/app/intermediates/merged-not-compiled-resources/release/drawable/launch_background.xml
build/app/intermediates/merged-not-compiled-resources/release/layout/notification_action_tombstone.xml
build/app/intermediates/merged-not-compiled-resources/release/layout/notification_action.xml
build/app/intermediates/merged-not-compiled-resources/release/layout/notification_template_icon_group.xml
build/app/intermediates/merged-not-compiled-resources/release/layout-v16/notification_template_custom_big.xml
build/app/intermediates/merged_manifest/release/AndroidManifest.xml
build/app/intermediates/merged_manifests/release/AndroidManifest.xml
build/app/intermediates/merged_native_libs/release/out/lib/arm64-v8a/libapp.so
build/app/intermediates/merged_native_libs/release/out/lib/armeabi-v7a/libapp.so
build/app/intermediates/merged_native_libs/release/out/lib/x86_64/libapp.so
build/app/intermediates/merged_res/release/drawable_launch_background.xml.flat
build/app/intermediates/merged_res/release/drawable_notification_bg_low.xml.flat
build/app/intermediates/merged_res/release/drawable_notification_bg.xml.flat
build/app/intermediates/merged_res/release/drawable_notification_icon_background.xml.flat
build/app/intermediates/merged_res/release/drawable_notification_tile_bg.xml.flat
build/app/intermediates/merged_res/release/drawable-hdpi-v4_notification_bg_low_normal.9.png.flat
build/app/intermediates/merged_res/release/drawable-hdpi-v4_notification_bg_low_pressed.9.png.flat
build/app/intermediates/merged_res/release/drawable-hdpi-v4_notification_bg_normal_pressed.9.png.flat
build/app/intermediates/merged_res/release/drawable-hdpi-v4_notification_bg_normal.9.png.flat
build/app/intermediates/merged_res/release/drawable-hdpi-v4_notify_panel_notification_icon_bg.png.flat
build/app/intermediates/merged_res/release/drawable-mdpi-v4_notification_bg_low_normal.9.png.flat
build/app/intermediates/merged_res/release/drawable-mdpi-v4_notification_bg_low_pressed.9.png.flat
build/app/intermediates/merged_res/release/drawable-mdpi-v4_notification_bg_normal_pressed.9.png.flat
build/app/intermediates/merged_res/release/drawable-mdpi-v4_notification_bg_normal.9.png.flat
build/app/intermediates/merged_res/release/drawable-mdpi-v4_notify_panel_notification_icon_bg.png.flat
build/app/intermediates/merged_res/release/drawable-v21_notification_action_background.xml.flat
build/app/intermediates/merged_res/release/drawable-xhdpi-v4_notification_bg_low_normal.9.png.flat
build/app/intermediates/merged_res/release/drawable-xhdpi-v4_notification_bg_low_pressed.9.png.flat
build/app/intermediates/merged_res/release/drawable-xhdpi-v4_notification_bg_normal_pressed.9.png.flat
build/app/intermediates/merged_res/release/drawable-xhdpi-v4_notification_bg_normal.9.png.flat
build/app/intermediates/merged_res/release/drawable-xhdpi-v4_notify_panel_notification_icon_bg.png.flat
build/app/intermediates/merged_res/release/layout_custom_dialog.xml.flat
build/app/intermediates/merged_res/release/layout_notification_action_tombstone.xml.flat
build/app/intermediates/merged_res/release/layout_notification_action.xml.flat
build/app/intermediates/merged_res/release/layout_notification_template_icon_group.xml.flat
build/app/intermediates/merged_res/release/layout_notification_template_part_chronometer.xml.flat
build/app/intermediates/merged_res/release/layout_notification_template_part_time.xml.flat
build/app/intermediates/merged_res/release/layout-v16_notification_template_custom_big.xml.flat
build/app/intermediates/merged_res/release/layout-v21_notification_action_tombstone.xml.flat
build/app/intermediates/merged_res/release/layout-v21_notification_action.xml.flat
build/app/intermediates/merged_res/release/layout-v21_notification_template_custom_big.xml.flat
build/app/intermediates/merged_res/release/layout-v21_notification_template_icon_group.xml.flat
build/app/intermediates/merged_res_blame_folder/release/out/single/release.json
build/app/intermediates/metadata_library_dependencies_report/release/dependencies.pb
build/app/intermediates/optimized_processed_res/release/resources-release-optimize.ap_
build/app/intermediates/packaged_manifests/release/AndroidManifest.xml
build/app/intermediates/processed_res/release/out/resources-release.ap_
build/app/intermediates/sdk_dependency_data/release/sdkDependencyData.pb
build/app/intermediates/shrunk_processed_res/release/resources-release-proto-stripped.ap_
build/app/intermediates/shrunk_processed_res/release/resources-release-stripped.ap_
build/app/intermediates/stripped_native_libs/release/out/lib/arm64-v8a/libapp.so
build/app/intermediates/stripped_native_libs/release/out/lib/armeabi-v7a/libapp.so
build/app/intermediates/stripped_native_libs/release/out/lib/x86_64/libapp.so
build/app/kotlin/compileReleaseKotlin/cacheable/last-build.bin
build/app/kotlin/compileReleaseKotlin/local-state/build-history.bin
build/app/outputs/apk/release/app-release.apk
build/app/outputs/flutter-apk/app-release.apk
build/app/outputs/flutter-apk/app-release.apk.sha1
build/app/outputs/logs/manifest-merger-release-report.txt
build/app/outputs/mapping/release/configuration.txt
build/app/outputs/mapping/release/mapping.txt
build/app/outputs/mapping/release/resources.txt
build/app/outputs/mapping/release/seeds.txt
build/app/outputs/mapping/release/usage.txt
build/app/outputs/sdk-dependencies/release/sdkDependencies.txt
build/app/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
android/app/build.gradle
android/app/src/main/AndroidManifest.xml
.dart_tool/package_config_subset
.dart_tool/package_config.json
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/.filecache
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/aot_android_asset_bundle.stamp
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/app.dill
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/flutter_assets.d
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/gen_dart_plugin_registrant.stamp
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/kernel_snapshot.d
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/kernel_snapshot.stamp
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/outputs.json
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/arm64-v8a/app.so
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/armeabi-v7a/app.so
.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/x86_64/app.so
