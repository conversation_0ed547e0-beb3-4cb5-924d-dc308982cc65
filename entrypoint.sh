#!/bin/bash

set -e

echo "🚀 Starting Flutter Hello World setup..."

# Update package lists
apt-get update -qq

# Install required packages
apt-get install -y --no-install-recommends \
    wget \
    unzip \
    git \
    curl \
    openjdk-11-jdk \
    build-essential

# Set JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

# Set Flutter environment variables
export FLUTTER_ROOT=/opt/flutter
export PATH=$FLUTTER_ROOT/bin:$PATH

# Create Android SDK directory
mkdir -p /opt/android-sdk

# Download and install Android command line tools
cd /opt/android-sdk
wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip
unzip -q commandlinetools-linux-9477386_latest.zip
mkdir -p cmdline-tools/latest
mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true
rm commandlinetools-linux-9477386_latest.zip

# Set Android environment variables
export ANDROID_HOME=/opt/android-sdk
export ANDROID_SDK_ROOT=/opt/android-sdk
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools

# Verify Flutter is available
echo "🔍 Checking Flutter installation..."
which flutter
flutter --version

# Accept Android licenses
yes | sdkmanager --licenses >/dev/null 2>&1

# Install Android SDK components
echo "📱 Installing Android SDK components..."
sdkmanager "platform-tools" "platforms;android-33" "build-tools;33.0.2"

# Navigate to app directory
cd /app

# Check if this is a new Flutter project
if [ ! -f "pubspec.yaml" ]; then
    echo "🎯 Creating new Flutter project..."
    flutter create . --project-name hello_world_app
fi

# Get Flutter dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

# Run Flutter doctor to check setup
echo "🔍 Running Flutter doctor..."
flutter doctor

# Build Android APK
echo "🔨 Building Android APK..."
flutter build apk --release

echo "✅ Flutter Hello World app setup complete!"
echo "📱 APK built at: build/app/outputs/flutter-apk/app-release.apk"

# Keep container running
echo "🎉 Setup complete! Container is ready for development."
echo "Run 'docker-compose exec flutter-app bash' to access the container."

# Start an interactive shell
exec bash
