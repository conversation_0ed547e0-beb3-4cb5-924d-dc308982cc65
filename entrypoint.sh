#!/bin/bash

set -e

echo "🚀 Starting Flutter Hello World setup..."

# Update package lists
echo "📋 Updating package lists..."
apt-get update -qq
echo "✅ Package lists updated"

# Install required packages
echo "📦 Installing required packages (wget, unzip, git, curl, openjdk-11-jdk, build-essential)..."
apt-get install -y --no-install-recommends \
    wget \
    unzip \
    git \
    curl \
    openjdk-11-jdk \
    build-essential
echo "✅ Required packages installed"

# Set JAVA_HOME
echo "☕ Setting JAVA_HOME environment variable..."
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
echo "✅ JAVA_HOME set to: $JAVA_HOME"

# Set Flutter environment variables
echo "🎯 Setting Flutter environment variables..."
export FLUTTER_ROOT=/opt/flutter
export PATH=$FLUTTER_ROOT/bin:$PATH
echo "✅ Flutter environment variables set"

# Create Android SDK directory
echo "📁 Creating Android SDK directory..."
mkdir -p /opt/android-sdk
echo "✅ Android SDK directory created"

# Download and install Android command line tools
echo "⬇️ Downloading Android command line tools..."
cd /opt/android-sdk
wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip
echo "✅ Android command line tools downloaded"

echo "📂 Extracting and organizing command line tools..."
unzip -q commandlinetools-linux-9477386_latest.zip
mkdir -p cmdline-tools/latest
mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true
rm commandlinetools-linux-9477386_latest.zip
echo "✅ Command line tools extracted and organized"

# Set Android environment variables
echo "🤖 Setting Android environment variables..."
export ANDROID_HOME=/opt/android-sdk
export ANDROID_SDK_ROOT=/opt/android-sdk
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools
echo "✅ Android environment variables set"

# Verify Flutter is available
echo "🔍 Checking Flutter installation..."
which flutter
flutter --version
echo "✅ Flutter installation verified"

# Accept Android licenses
echo "📜 Accepting Android SDK licenses..."
yes | sdkmanager --licenses >/dev/null 2>&1
echo "✅ Android SDK licenses accepted"

# Install Android SDK components
echo "📱 Installing Android SDK components (platform-tools, android-33, build-tools)..."
sdkmanager "platform-tools" "platforms;android-33" "build-tools;33.0.2"
echo "✅ Android SDK components installed"

# Navigate to app directory
echo "📂 Navigating to app directory..."
cd /app
echo "✅ Now in app directory: $(pwd)"

# Check if Android directory exists (required for Android compilation)
if [ ! -d "android" ]; then
    echo "🎯 Android directory not found - creating new Flutter project with Android support..."
    # Create a temporary directory for the new project
    echo "📁 Creating temporary directory for Flutter project generation..."
    mkdir -p /tmp/flutter_temp
    cd /tmp/flutter_temp
    echo "✅ Temporary directory created"

    echo "🛠️ Generating Flutter project..."
    flutter create hello_world_app --project-name hello_world_app
    echo "✅ Flutter project generated"

    # Copy the generated Android structure to our app directory
    echo "📋 Copying Android project structure to app directory..."
    cp -r hello_world_app/android /app/
    cp -r hello_world_app/.metadata /app/ 2>/dev/null || true
    echo "✅ Android project structure copied"

    # Clean up
    echo "🧹 Cleaning up temporary files..."
    rm -rf /tmp/flutter_temp
    cd /app
    echo "✅ Cleanup complete, back in app directory"

    echo "✅ Android project structure created!"
else
    echo "✅ Android directory already exists"
fi

# Get Flutter dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get
echo "✅ Flutter dependencies retrieved"

# Run Flutter doctor to check setup
echo "🔍 Running Flutter doctor to verify setup..."
flutter doctor
echo "✅ Flutter doctor check complete"

# Build Android APK
echo "🔨 Building Android APK (release mode)..."
flutter build apk --release
echo "✅ Android APK build complete"

echo "✅ Flutter Hello World app setup complete!"
echo "📱 APK built at: build/app/outputs/flutter-apk/app-release.apk"

# Keep container running
echo "🎉 Setup complete! Container is ready for development."
echo "Run 'docker compose exec flutter-app bash' to access the container."

# Start an interactive shell
echo "🖥️ Starting interactive shell..."
exec bash
