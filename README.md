# Flutter Hello World App with Docker

A simple Flutter "Hello World" application that compiles to Android using Docker Compose.

## Features

- 🌍 Simple Hello World Flutter app
- 🐳 Docker Compose setup (no Dockerfile needed)
- 📱 Android APK compilation
- 🚀 Automated setup with entrypoint.sh

## Quick Start

1. **Make the entrypoint script executable:**
   ```bash
   chmod +x entrypoint.sh
   ```

2. **Build and run the container:**
   ```bash
   docker-compose up --build
   ```

3. **The setup will automatically:**
   - Install Android SDK and build tools
   - Create the Flutter project (if needed)
   - Build the Android APK
   - Keep the container running for development

4. **Find your APK:**
   The compiled APK will be available at:
   ```
   build/app/outputs/flutter-apk/app-release.apk
   ```

## Development

To access the container for development:
```bash
docker-compose exec flutter-app bash
```

Inside the container, you can run Flutter commands:
```bash
flutter run
flutter build apk
flutter doctor
```

## Project Structure

```
├── docker-compose.yml      # Docker Compose configuration
├── entrypoint.sh          # Setup script
├── lib/
│   └── main.dart          # Main Flutter app
├── pubspec.yaml           # Flutter dependencies
└── README.md              # This file
```

## Requirements

- Docker
- Docker Compose

The container handles all Flutter and Android SDK dependencies automatically!
