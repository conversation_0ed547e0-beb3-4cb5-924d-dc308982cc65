import 'package:flutter/services.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'dart:typed_data';

class TFLiteService {
  Interpreter? _interpreter;
  List<int>? _inputShape;
  List<int>? _outputShape;

  Future<void> loadModel() async {
    try {
      // Load the model from assets
      final ByteData modelBytes = await rootBundle.load('assets/models/gesture_model1.tflite');
      final Uint8List modelData = modelBytes.buffer.asUint8List();
      
      // Create interpreter with options
      final options = InterpreterOptions()..threads = 4;
      _interpreter = Interpreter.fromBuffer(modelData, options: options);
      
      // Get input and output tensor information
      final inputTensors = _interpreter!.getInputTensors();
      final outputTensors = _interpreter!.getOutputTensors();

      if (inputTensors.isNotEmpty) {
        _inputShape = inputTensors[0].shape;
      }

      if (outputTensors.isNotEmpty) {
        _outputShape = outputTensors[0].shape;
      }

      print('Model loaded successfully');
      print('Input shape: $_inputShape');
      print('Output shape: $_outputShape');
      
    } catch (e) {
      print('Error loading model: $e');
      rethrow;
    }
  }

  List<double> runInference(List<List<double>> points) {
    if (_interpreter == null) {
      throw Exception('Model not loaded. Call loadModel() first.');
    }

    try {
      // Flatten the points into a single list
      List<double> flatPoints = [];
      for (var point in points) {
        flatPoints.addAll(point);
      }

      // Pad or truncate to expected input size
      int expectedSize = _inputShape!.reduce((a, b) => a * b);
      if (flatPoints.length < expectedSize) {
        // Pad with zeros
        flatPoints.addAll(List.filled(expectedSize - flatPoints.length, 0.0));
      } else if (flatPoints.length > expectedSize) {
        // Truncate
        flatPoints = flatPoints.sublist(0, expectedSize);
      }

      // Create input tensor - use the shape directly
      final inputData = [flatPoints];

      // Create output tensor
      final outputSize = _outputShape!.reduce((a, b) => a * b);
      final output = List.generate(_outputShape![0], (i) => List.filled(_outputShape!.length > 1 ? _outputShape![1] : outputSize, 0.0));

      // Run inference
      _interpreter!.run(inputData, output);

      // Return flattened output
      if (output.isNotEmpty && output[0] is List) {
        return (output[0] as List<double>);
      } else {
        return output.cast<double>();
      }

    } catch (e) {
      print('Error during inference: $e');
      rethrow;
    }
  }

  void close() {
    _interpreter?.close();
    _interpreter = null;
  }
}
