services:
  flutter-app:
    image: cirrusci/flutter:stable
    container_name: flutter_hello_world
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - FLUTTER_ROOT=/opt/flutter
      - ANDROID_HOME=/opt/android-sdk
      - ANDROID_SDK_ROOT=/opt/android-sdk
      - PATH=/opt/flutter/bin:/opt/android-sdk/cmdline-tools/latest/bin:/opt/android-sdk/platform-tools:$PATH
    entrypoint: ["/app/entrypoint.sh"]
    stdin_open: true
    tty: true
    ports:
      - "8080:8080"
    networks:
      - flutter_network

networks:
  flutter_network:
    driver: bridge
