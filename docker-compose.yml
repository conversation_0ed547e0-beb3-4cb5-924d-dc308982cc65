services:
  flutter-app:
    image: cirrusci/flutter:stable
    container_name: flutter_hello_world
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - FLUTTER_ROOT=/opt/flutter
      - ANDROID_HOME=/opt/android-sdk
      - ANDROID_SDK_ROOT=/opt/android-sdk
      - JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
    entrypoint: ["/app/entrypoint.sh"]
    stdin_open: true
    tty: true
    ports:
      - "8080:8080"
    networks:
      - flutter_network

networks:
  flutter_network:
    driver: bridge
